import {
  createParamDecorator,
  ExecutionContext,
  Injectable,
} from '@nestjs/common';
import { AdminAuthService } from './adminAuth.service';

@Injectable()
export class GetOptionalUserDecorator {
  constructor(private readonly authService: AdminAuthService) {}

  public getOptionalUser = createParamDecorator(
    async (data: unknown, ctx: ExecutionContext) => {
      const request = ctx.switchToHttp().getRequest();
      const authHeader = request.headers.authorization;

      if (authHeader && authHeader.startsWith('Bearer ')) {
        const sessionToken = authHeader.split(' ')[1];
        try {
          // Use the authService to verify the session
          const user = await this.authService.verifySession(sessionToken);
          console.log('User:', user);
          return user;
        } catch (error) {
          console.log('Session verification error:', error);
          return null;
        }
      }
      return null;
    },
  );
}
