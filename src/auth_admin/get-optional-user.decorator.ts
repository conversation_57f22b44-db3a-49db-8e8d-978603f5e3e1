import {
  createParamDecorator,
  ExecutionContext,
} from '@nestjs/common';
import { AdminAuthService } from './adminAuth.service';

export const GetOptionalUser = createParamDecorator(
  async (data: unknown, ctx: ExecutionContext) => {
    const request = ctx.switchToHttp().getRequest();
    const authHeader = request.headers.authorization;

    if (authHeader && authHeader.startsWith('Bearer ')) {
      const sessionToken = authHeader.split(' ')[1];
      try {
        // Get the AdminAuthService from the application context
        const app = ctx.switchToHttp().getRequest().app;
        const authService = app.get(AdminAuthService);

        // Use the authService to verify the session
        const user = await authService.verifySession(sessionToken);
        console.log('User:', user);
        return user;
      } catch (error) {
        console.log('Session verification error:', error);
        return null;
      }
    }
    return null;
  },
);
